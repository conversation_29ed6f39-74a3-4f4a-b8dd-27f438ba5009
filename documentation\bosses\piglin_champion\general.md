# Piglin Champion: General Information

## Basic Statistics

| Attribute | Value |
|-----------|-------|
| Health | 1200 |
| Collision Box | Width: 2.3 blocks, Height: 4.7 blocks |
| Movement Speed | 0.15 blocks per second (base speed) |
| Boss Bar Range | 32 blocks |
| Target Detection Range | 32 blocks |
| Type Family | "piglin_champion", "boss" |

## Resistances and Immunities

The Piglin Champion has several damage immunities and resistances:

- **Immune to damage** during spawning animation
- **Immune to damage** during healing ability
- **Immune to damage** when health is at or below 10 points
- **Immune to damage** when marked as dead

## Movement and Navigation

The Piglin Champion uses the following movement components:

- **Basic Movement**: Uses `minecraft:movement.basic`
- **Navigation**: Uses `minecraft:navigation.walk` with the following capabilities:
  - Can avoid damage blocks
  - Can pass through doors
  - Can jump
  - Is amphibious (can move through water)
  - Can float

## Targeting Behavior

The Piglin Champion targets entities with the following priorities:

1. **Players** (Priority 0)
   - Maximum distance: 32 blocks
   - Excludes players in Creative or Spectator mode

2. **Other Boss Entities** (Priority 2)
   - Maximum distance: 32 blocks
   - Excludes other Piglin Champions

Targeting properties:
- Must see target
- Attack interval: 1 tick
- Reselects targets continuously
- Forgets unseen targets after 8 seconds

## Attacks and Damage

The Piglin Champion has multiple attack types with varying damage values, organized by range:

### Close Range Attacks (0-7 blocks)

#### Horizontal Attack
- **Damage**: 7 health points
- **Knockback**: 8 blocks horizontally, 0.8 blocks vertically
- **Range**: 4 block radius
- **Execution**: Applies damage and knockback to entities in front of the piglin
- **Duration**: 104 ticks (5.2 seconds)
- **Effect Timing**: Tick 38

#### Vertical Attack
- **Axe Damage**: 6 health points
- **Rock Damage**: 4 health points
- **Knockback**: Upward knockback (10 blocks)
- **Range**: 5 block radius
- **Execution**: Initial axe damage followed by spawning rocks in a wave pattern
- **Duration**: 106 ticks (5.3 seconds)
- **Effect Timing**: Tick 44

#### Foot Stomp Attack
- **Damage**: 3 health points
- **Knockback**: 3 blocks horizontally, 0.5 blocks vertically
- **Range**: 5 block radius
- **Execution**: Applies damage and knockback to entities in all directions
- **Duration**: 80 ticks (4.0 seconds)
- **Effect Timing**: Tick 24

#### Spin Slam Attack
- **Two phases**:
  - Phase 1 (tick 83): Lifts entities 7 blocks and deals 7 damage
  - Phase 2 (tick 124): Slams entities down with additional effects
- **Range**: Varies by phase
- **Execution**: First lifts entities, then slams them down
- **Duration**: 178 ticks (8.9 seconds)
- **Effect Timing**: Tick 83 (phase 1), Tick 124 (phase 2)

#### Body Slam Attack
- **Damage**: 10 health points
- **Knockback**: 5 blocks horizontally, 0.5 blocks vertically
- **Range**: 8 block radius
- **Execution**: Applies damage and knockback to entities in all directions
- **Duration**: 186 ticks (9.3 seconds)
- **Effect Timing**: Tick 63

### Medium Range Attacks (7-9 blocks)

Includes Horizontal, Vertical, Foot Stomp, Spin Slam (same as close range), plus:

#### Summoning Chant Attack
- **Effect**: Summons 3 piglin brutes with a 15-tick delay between each
- **Range**: Spawns minions 3-7 blocks away from the piglin
- **Execution**: Summons minions progressively with particle effects
- **Duration**: 125 ticks (6.25 seconds)
- **Effect Timing**: Tick 40

### Long Range Attacks (9-10 blocks)

Includes Horizontal, Vertical, Summoning Chant (same as medium range).

### Unreachable Range Attacks (12-24 blocks)

#### Charging Attack
- **Continuous Damage**: Applied every 4 ticks from tick 46-90 during charge movement
- **Final Impact Damage**: Applied at tick 96 with knockback
- **Damage**: 20 damage per hit (consistent throughout continuous and final impact)
- **Knockback**: 6 blocks horizontally, 0.8 blocks vertically (final impact only)
- **Multiple phases**:
  - Initial charge (0-46 ticks)
  - Charge 2 (46-85 ticks): Speed 3 effect applied at tick 30, continuous damage starts at tick 46
  - Charge 3 (85-133 ticks): Continuous damage continues until tick 90
  - Stunned sitting (133-253 ticks): Slowness effect applied at tick 90
  - Charge 4 (253-315 ticks)
- **Duration**: 315 ticks (15.75 seconds)
- **Effect Timings**:
  - Continuous damage: Tick 46-90 (every 4 ticks)
  - Final impact: Tick 96

### Upchuck Attack
- **Trigger**: Automatically after healing ability completes
- **Duration**: 170 ticks (8.5 seconds)
- **Effect Timing**: Tick 55

## Special Abilities

### Healing Ability

- Triggers at 75%, 50%, and 25% health
- Heals 10% of max health (120 health points) progressively between ticks 43-88
- Makes the Piglin Champion invulnerable during the entire animation
- Includes a knockback roar at tick 130
- Total duration: 7.9 seconds (158 ticks)
- Always followed by an upchuck attack

### Stun Mechanics

1. **Stunned Standing**
   - Triggered at 65% health
   - Duration: 12 seconds (240 ticks)
   - Applies slowness effect (amplifier 250)
   - Cancels any ongoing attack

2. **Stunned Sitting**
   - Triggered at 35% health
   - Duration: 16.65 seconds (333 ticks)
   - Applies slowness effect (amplifier 250)
   - Cancels any ongoing attack

## Essence Drops

Upon death, the Piglin Champion drops essence items:

### Piglin Champion Essence
- 32 essence items
- Spawned in a fountain-like effect using the `spawnItemFountain` function
- Items are spawned sequentially with alternating delays between each
- Creates a visually appealing distribution from the boss's location
- Enhanced with particle effects (`minecraft:large_explosion`)
- Accompanied by any sound effect (`random.pop` in this case)
- Customized impulse parameters for controlled spread and height

The essence items can be used for crafting powerful equipment or as valuable trading items. The fountain-like effect creates a dramatic visual display as the boss is defeated, with essence items shooting upward and then falling back down around the death location.

## Spawning and Death

### Spawning
- Starts with `ptd_dbb:spawning` set to true
- Spawning animation plays for 114 ticks (5.7 seconds)
- Applies a shockwave effect at tick 6
- Immune to all damage during spawning

### Death
- Triggered when health reaches 10 or below
- Death animation plays
- Essence items are spawned in a fountain-like effect (32 items) with particle and sound effects
- The fountain effect is triggered at tick 1 of the death sequence using a custom event
- Entity is removed after death animation completes

## Animation Controllers

The Piglin Champion uses multiple animation controllers:

1. **Main Controller** (`controller.animation.ptd_dbb_piglin_champion.general`)
   - Handles transitions between default, idling, walking, and attack states

2. **Stun Controller** (`controller.animation.ptd_dbb_piglin_champion.stun`)
   - Handles transitions between stun phases

Transitions are controlled by entity properties:
- `ptd_dbb:spawning` (boolean)
- `ptd_dbb:attack` (string - attack type)
- `ptd_dbb:attack_timer` (integer - current tick of the attack)
- `ptd_dbb:dead` (boolean)

## Dynamic Properties

The Piglin Champion uses several dynamic properties to track its state:

- `ptd_dbb:spawning` (boolean) - Whether the entity is in the spawning animation
- `ptd_dbb:spawning_ticks` (integer) - Current tick of the spawning animation
- `ptd_dbb:dead` (boolean) - Whether the entity is dead
- `ptd_dbb:death_timer` (integer) - Current tick of the death animation
- `ptd_dbb:attack` (string) - Current attack type
- `ptd_dbb:attack_timer` (integer) - Current tick of the attack animation
- `ptd_dbb:attack_cooldown` (integer) - Cooldown before next attack
- `ptd_dbb:last_heal_threshold` (integer) - Last health threshold for healing
- `ptd_dbb:stun_standing_triggered` (boolean) - Whether the standing stun has been triggered
- `ptd_dbb:stun_sitting_triggered` (boolean) - Whether the sitting stun has been triggered