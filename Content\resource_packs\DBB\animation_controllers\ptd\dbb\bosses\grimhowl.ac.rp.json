{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb_grimhowl.basic_root": {"initial_state": "spawning", "states": {"spawning": {"animations": ["spawn"], "transitions": [{"idle": "!query.property('ptd_dbb:spawning') || query.all_animations_finished"}, {"death": "query.property('ptd_dbb:dead')"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "idle": {"animations": [{"idle": "query.property('ptd_dbb:sword_mode')"}, {"idle_swordless": "!query.property('ptd_dbb:sword_mode')"}, "is_not_attacking"], "transitions": [{"walking": "q.ground_speed > 1"}, {"moves": "query.property('ptd_dbb:move_list') != 'none'"}, {"death": "query.property('ptd_dbb:dead')"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "walking": {"animations": [{"runsprintcontroller": "query.property('ptd_dbb:move_list') == 'none'"}, "is_not_attacking"], "transitions": [{"idle": "q.ground_speed < 0.5"}, {"moves": "query.property('ptd_dbb:move_list') != 'none'"}, {"death": "query.property('ptd_dbb:dead')"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "moves": {"animations": ["move_list_root"], "transitions": [{"idle": "query.property('ptd_dbb:move_list') == 'none'"}, {"death": "query.property('ptd_dbb:dead')"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "death": {"animations": [{"death": "query.property('ptd_dbb:dead')"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}, "controller.animation.ptd_dbb_grimhowl.runsprintcontroller": {"initial_state": "idle", "states": {"idle": {"animations": [{"walk": "1"}, "is_not_attacking"], "transitions": [{"walking": "query.ground_speed > 10"}], "blend_transition": 0.4, "blend_via_shortest_path": true}, "walking": {"animations": [{"sprint": "1"}, "is_not_attacking"], "transitions": [{"idle": "query.ground_speed <= 10"}], "blend_transition": 0.4, "blend_via_shortest_path": true}}}, "controller.animation.ptd_dbb_grimhowl.move_list_root": {"states": {"default": {"animations": ["is_not_attacking"], "transitions": [{"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}]}, "grimhowl_slash": {"animations": [{"grimhowl_slash": "query.property('ptd_dbb:sword_mode')"}, {"swordless_grimhowl_slash": "!query.property('ptd_dbb:sword_mode')"}], "transitions": [{"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'grimhowl_slash'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_spinning_slash": {"animations": [{"grimhowl_spinning_slash": "query.property('ptd_dbb:sword_mode')"}, {"swordless_grimhowl_spinning_slash": "!query.property('ptd_dbb:sword_mode')"}], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'grimhowl_spinning_slash'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_pounce": {"animations": [{"grimhowl_pounce": "query.property('ptd_dbb:sword_mode')"}, {"swordless_grimhowl_pounce": "!query.property('ptd_dbb:sword_mode')"}], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'grimhowl_pounce'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_shadow_onslaught": {"animations": [{"grimhowl_shadow_onslaught": "query.property('ptd_dbb:sword_mode')"}, {"swordless_grimhowl_shadow_onslaught": "!query.property('ptd_dbb:sword_mode')"}], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'grimhowl_shadow_onslaught'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_claw_left": {"animations": [{"grimhowl_claw_left": "query.property('ptd_dbb:sword_mode')"}, {"swordless_grimhowl_claw_left": "!query.property('ptd_dbb:sword_mode')"}], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'grimhowl_claw_left'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_claw_right": {"animations": [{"grimhowl_claw_right": "query.property('ptd_dbb:sword_mode')"}, {"swordless_grimhowl_claw_right": "!query.property('ptd_dbb:sword_mode')"}], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'grimhowl_claw_right'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_backstep": {"animations": [{"grimhowl_backstep_sword": "query.property('ptd_dbb:sword_mode')"}, {"grimhowl_backstep": "!query.property('ptd_dbb:sword_mode')"}], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'grimhowl_backstep'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_roar": {"animations": ["grimhowl_roar"], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'grimhowl_roar'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_swordless_transition": {"animations": ["grimhowl_swordless_transition"], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'transition_to_swordless'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_sword_transition": {"animations": ["grimhowl_sword_transition"], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_arrow_shake": "query.property('ptd_dbb:move_list') == 'arrow_shake'"}, {"default": "query.property('ptd_dbb:move_list') != 'transition_to_sword'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "grimhowl_arrow_shake": {"animations": ["grimhowl_arrow_shake"], "transitions": [{"grimhowl_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_slash'"}, {"grimhowl_spinning_slash": "query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash'"}, {"grimhowl_shadow_onslaught": "query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught'"}, {"grimhowl_pounce": "query.property('ptd_dbb:move_list') == 'grimhowl_pounce'"}, {"grimhowl_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"grimhowl_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"grimhowl_backstep": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep'"}, {"grimhowl_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar'"}, {"grimhowl_swordless_transition": "query.property('ptd_dbb:move_list') == 'transition_to_swordless'"}, {"grimhowl_sword_transition": "query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"default": "query.property('ptd_dbb:move_list') != 'arrow_shake'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}}}