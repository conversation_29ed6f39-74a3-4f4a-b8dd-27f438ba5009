/**
 * Interface for attack damage configuration
 */
interface AttackDamage {
  /**
   * Direct damage value to apply
   */
  damage: number;
}

/**
 * Interface for Piglin Champion attack damages
 */
interface PiglinChampionAttackDamages {
  horizontal: AttackDamage;
  vertical: {
    axe: AttackDamage;
    rocks: AttackDamage;
  };
  foot_stomp: AttackDamage;
  spin_slam: AttackDamage;
  body_slam: AttackDamage;
  charging: AttackDamage;
  summoning_chant?: AttackDamage; // Optional as it might not deal damage
}

/**
 * Interface for Necromancer attack damages
 */
interface NecromancerAttackDamages {
  cataclysm: AttackDamage;
  soul_drain: AttackDamage;
  // Add other attacks as they are implemented
}

/**
 * Piglin Champion attack damages as direct values
 */
export const PIGLIN_CHAMPION_ATTACK_DAMAGES: PiglinChampionAttackDamages = {
  horizontal: { damage: 16 },
  vertical: {
    axe: { damage: 16 },
    rocks: { damage: 8 }
  },
  foot_stomp: { damage: 12 },
  spin_slam: { damage: 12 },
  body_slam: { damage: 18 },
  charging: { damage: 20 }
};

/**
 * Necromancer attack damages as direct values
 */
export const NECROMANCER_ATTACK_DAMAGES: NecromancerAttackDamages = {
  cataclysm: { damage: 6 },
  soul_drain: { damage: 7 }
};
