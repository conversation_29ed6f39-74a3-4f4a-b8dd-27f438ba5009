/**
 * Utility functions for Grimhowl boss logic.
 * This file contains helper functions for target filtering and position checking.
 */
import { <PERSON><PERSON><PERSON>, <PERSON> } from "@minecraft/server";

/**
 * Creates a filter function to identify valid targets for Grimhowl attacks
 * Excludes the source entity, creative/spectator players, inanimate objects,
 * other bosses, and projectiles
 *
 * @param sourceEntity - The Grimhowl boss entity
 * @returns A filter function that returns true for valid targets
 */
export const filterValidTargets = (sourceEntity: Entity) => (entity: Entity) => {
    if (entity === sourceEntity) return false;

    if (entity instanceof Player) {
        const gameMode = entity.getGameMode();
        if (gameMode === "creative" || gameMode === "spectator") return false;
    }

    const typeFamilyComponent = entity.getComponent("minecraft:type_family");
    if (typeFamilyComponent && typeFamilyComponent.hasTypeFamily('inanimate')) return false;
    if (typeFamilyComponent && typeFamilyComponent.hasTypeFamily('boss')) return false;

    return !entity.hasComponent("minecraft:projectile");
};

/**
 * Determines if an entity is in front of a target entity
 * Uses dot product of view direction and direction to entity
 *
 * @param target - The reference entity (usually the Grimhowl boss)
 * @param entity - The entity to check
 * @param threshold - The dot product threshold (default: 0.5)
 * @returns True if the entity is in front of the target
 */
export function isEntityInFront(target: Entity, entity: Entity, threshold = 0.5) {
    const viewDirection = target.getViewDirection();
    const directionToEntity = {
        x: entity.location.x - target.location.x,
        y: entity.location.y - target.location.y,
        z: entity.location.z - target.location.z
    };
    const magnitude = Math.sqrt(directionToEntity.x ** 2 + directionToEntity.y ** 2 + directionToEntity.z ** 2);
    const normalizedDirection = {
        x: directionToEntity.x / magnitude,
        y: directionToEntity.y / magnitude,
        z: directionToEntity.z / magnitude
    };
    const dotProduct = viewDirection.x * normalizedDirection.x + viewDirection.z * normalizedDirection.z;
    return dotProduct > threshold;
}

