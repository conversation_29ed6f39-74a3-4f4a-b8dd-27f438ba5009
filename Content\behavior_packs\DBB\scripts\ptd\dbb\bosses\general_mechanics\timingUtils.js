import { system } from "@minecraft/server";
/**
 * Map to store active timeouts for cleanup
 */
const activeTimeouts = new Map();
/**
 * Map to store active intervals for cleanup
 */
const activeIntervals = new Map();
/**
 * Global timeout function that wraps system.runTimeout
 * Provides additional features like tracking and cleanup
 *
 * @param config - Configuration object for the timeout
 * @returns The timeout ID for potential cleanup
 */
export function globalRunTimeout(config) {
    const timeoutId = system.runTimeout(() => {
        try {
            config.callback();
        }
        catch (error) {
            console.warn(`Error in global timeout ${config.id || 'unknown'}: ${error}`);
        }
        finally {
            // Clean up the timeout from tracking
            activeTimeouts.delete(timeoutId);
        }
    }, config.delay);
    // Track the timeout
    activeTimeouts.set(timeoutId, config.id || 'unknown');
    return timeoutId;
}
/**
 * Global interval function that wraps system.runInterval
 * Provides additional features like tracking, cleanup, and execution limits
 *
 * @param config - Configuration object for the interval
 * @returns The interval ID for potential cleanup
 */
export function globalRunInterval(config) {
    const intervalData = {
        id: config.id,
        executions: 0,
        maxExecutions: config.maxExecutions
    };
    const intervalId = system.runInterval(() => {
        try {
            // Check if we've reached the maximum executions
            if (intervalData.maxExecutions && intervalData.executions >= intervalData.maxExecutions) {
                clearGlobalInterval(intervalId);
                return;
            }
            config.callback();
            intervalData.executions++;
        }
        catch (error) {
            console.warn(`Error in global interval ${config.id || 'unknown'}: ${error}`);
            // Don't clear the interval on error unless it's critical
        }
    }, config.interval);
    // Track the interval
    activeIntervals.set(intervalId, intervalData);
    return intervalId;
}
/**
 * Clears a global timeout
 *
 * @param timeoutId - The timeout ID to clear
 */
export function clearGlobalTimeout(timeoutId) {
    system.clearRun(timeoutId);
    activeTimeouts.delete(timeoutId);
}
/**
 * Clears a global interval
 *
 * @param intervalId - The interval ID to clear
 */
export function clearGlobalInterval(intervalId) {
    system.clearRun(intervalId);
    activeIntervals.delete(intervalId);
}
/**
 * Clears all active timeouts and intervals
 * Useful for cleanup when entities are removed or on world unload
 */
export function clearAllGlobalTimers() {
    // Clear all timeouts
    for (const timeoutId of activeTimeouts.keys()) {
        system.clearRun(timeoutId);
    }
    activeTimeouts.clear();
    // Clear all intervals
    for (const intervalId of activeIntervals.keys()) {
        system.clearRun(intervalId);
    }
    activeIntervals.clear();
}
/**
 * Gets information about active timers for debugging
 *
 * @returns Object containing counts and details of active timers
 */
export function getActiveTimersInfo() {
    return {
        timeouts: {
            count: activeTimeouts.size,
            ids: Array.from(activeTimeouts.values())
        },
        intervals: {
            count: activeIntervals.size,
            details: Array.from(activeIntervals.values())
        }
    };
}
/**
 * Utility function to create a delay using async/await pattern
 * Similar to system.waitTicks but can be used in non-async contexts
 *
 * @param ticks - Number of ticks to wait
 * @returns Promise that resolves after the specified ticks
 */
export function createDelay(ticks) {
    return new Promise((resolve) => {
        globalRunTimeout({
            callback: () => resolve(),
            delay: ticks,
            id: 'delay'
        });
    });
}
/**
 * Utility function to execute a callback after a delay with error handling
 *
 * @param callback - Function to execute
 * @param delay - Delay in ticks
 * @param errorHandler - Optional error handler
 * @returns The timeout ID
 */
export function executeAfterDelay(callback, delay, errorHandler) {
    return globalRunTimeout({
        callback: () => {
            try {
                callback();
            }
            catch (error) {
                if (errorHandler) {
                    errorHandler(error);
                }
                else {
                    console.warn(`Error in delayed execution: ${error}`);
                }
            }
        },
        delay,
        id: 'delayed-execution'
    });
}
/**
 * Utility function to execute a callback repeatedly with a limit
 *
 * @param callback - Function to execute
 * @param interval - Interval in ticks
 * @param maxExecutions - Maximum number of executions
 * @param errorHandler - Optional error handler
 * @returns The interval ID
 */
export function executeRepeated(callback, interval, maxExecutions, errorHandler) {
    return globalRunInterval({
        callback: () => {
            try {
                callback();
            }
            catch (error) {
                if (errorHandler) {
                    errorHandler(error);
                }
                else {
                    console.warn(`Error in repeated execution: ${error}`);
                }
            }
        },
        interval,
        maxExecutions,
        id: 'repeated-execution'
    });
}
