{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb_piglin_champion.stun": {"initial_state": "default", "states": {"default": {"transitions": [{"stunned_standing": "q.property('ptd_dbb:attack') == 'stunned_standing'"}, {"stunned_sitting": "q.property('ptd_dbb:attack') == 'stunned_sitting'"}]}, "stunned_standing": {"animations": ["damage_to_stunned"], "transitions": [{"stunned_standing_phase2": "q.property('ptd_dbb:attack_timer') == 30"}, {"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "stunned_standing_phase2": {"animations": ["stunned_standing"], "transitions": [{"stunned_standing_phase3": "q.property('ptd_dbb:attack_timer') == 150"}, {"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "stunned_standing_phase3": {"animations": ["stunned_to_idle"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "stunned_sitting": {"animations": ["damage_to_stunned"], "transitions": [{"stunned_sitting_phase2": "q.property('ptd_dbb:attack_timer') == 30"}, {"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "stunned_sitting_phase2": {"animations": ["stunned_sitting"], "transitions": [{"stunned_sitting_phase3": "q.property('ptd_dbb:attack_timer') == 150"}, {"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "stunned_sitting_phase3": {"animations": ["stunned_sitting_to_idle"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "dead": {"animations": ["death"]}}}}}