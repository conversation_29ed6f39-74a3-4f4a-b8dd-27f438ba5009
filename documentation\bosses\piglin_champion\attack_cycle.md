# Piglin Champion Attack Cycle Documentation

## 1. Overview

The Piglin Champion features a sophisticated attack system with multiple attack types, dynamic selection based on distance, attack history tracking, and special mechanics triggered by health thresholds. This document focuses on the attack cycle flow - how attacks are selected, executed, and completed.

## 2. Attack Selection Logic

### 2.1 Distance-Based Selection

The Piglin Champion selects attacks based on the distance to the target player:

| Range Name | Distance (blocks) | Available Attacks |
|------------|-------------------|-------------------|
| Close | 0-7 | horizontal, vertical, foot_stomp, spin_slam, body_slam |
| Medium | 7-9 | horizontal, vertical, foot_stomp, spin_slam, summoning_chant |
| Long | 9-10 | horizontal, vertical, summoning_chant |
| Unreachable | 12-24 | charging, summoning_chant (rare) |

### 2.2 Attack History Tracking

To ensure variety in attacks, the system tracks which attacks have been used:

- Maintains separate history for short, medium, and long ranges
- Prioritizes unused attacks, then attacks used only once
- Resets history when all attacks have been used twice
- Uses dynamic properties to store attack history:
  - `shortRangeAttackHistory`
  - `mediumRangeAttackHistory`
  - `longRangeAttackHistory`

### 2.3 Health Threshold Checks

Before selecting an attack based on distance, the system checks:

1. **Healing Threshold**: Triggers healing at 75%, 50%, and 25% health
2. **Stun Thresholds**:
   - Stunned Standing at 65% health
   - Stunned Sitting at 35% health

These special mechanics take priority over regular attack selection.

## 3. Animation Controllers

### 3.1 Main Controller (controller.animation.ptd_dbb_piglin_champion.general)

Handles transitions between:
- default (spawning)
- idling
- walking
- attack states (horizontal, vertical, foot_stomp, spin_slam, body_slam, upchuck, charging, healing, summoning_chant)
- stunned state

### 3.2 Stun Controller (controller.animation.ptd_dbb_piglin_champion.stun)

Handles transitions between:
- default
- stunned_standing (phase 1)
- stunned_standing_phase2
- stunned_standing_phase3
- stunned_sitting (phase 1)
- stunned_sitting_phase2
- stunned_sitting_phase3
- dead

### 3.3 Transition Logic

Transitions are controlled by entity properties:
- `ptd_dbb:spawning` (boolean)
- `ptd_dbb:attack` (string - attack type)
- `ptd_dbb:attack_timer` (integer - current tick of the attack)
- `ptd_dbb:dead` (boolean)

For example:
- Transition from idling to horizontal_attack: `q.property('ptd_dbb:attack') == 'horizontal'`
- Transition from charging to charging_2: `q.property('ptd_dbb:attack_timer') == 46`

## 4. Component Groups and Events

The attack cycle is influenced by events that modify component groups:
- `ptd_dbb:horizontal_attack`
- `ptd_dbb:vertical_attack`
- `ptd_dbb:foot_stomp_attack`
- `ptd_dbb:spin_slam_attack`
- `ptd_dbb:body_slam_attack`
- `ptd_dbb:upchuck_attack`
- `ptd_dbb:charging_attack`
- `ptd_dbb:charging2`
- `ptd_dbb:stun_after_charge`
- `ptd_dbb:healing_ability`
- `ptd_dbb:stunned_standing`
- `ptd_dbb:stunned_sitting`
- `ptd_dbb:reset_attack`
- `ptd_dbb:dead`

## 5. Sound Management

- Each attack has a corresponding sound effect defined in `ATTACK_SOUND_MAP`
- When selecting a new attack, all other sound effects are stopped except for the selected attack's sound
- The `stopPiglinChampionSounds` function is used to stop sounds when resetting attacks or transitioning between states
- Sound effects are played through `entity.runCommand()` with the "stopsound" command

## 6. Complete Attack Cycle Flow

### 6.1 Initialization

- When the Piglin Champion is spawned, `ptd_dbb:spawning` is set to true
- The spawning animation plays for 114 ticks
- At tick 6, a shockwave effect is applied
- After spawning completes, `ptd_dbb:spawning` is set to false and the entity transitions to the idling state

### 6.2 Attack Selection

- If not in an attack and cooldown is 0, the system checks:
  1. If health threshold crossed for healing (priority)
  2. If health threshold crossed for stun (65% or 35%)
  3. Distance to nearest player
  4. Available attacks based on attack history
- An attack is selected based on these factors
- The corresponding event is triggered (e.g., `ptd_dbb:horizontal_attack`)
- The attack history is updated
- Sound effects for other attacks are stopped

### 6.3 Attack Execution

- The animation controller transitions to the attack state
- The attack timer increments each tick
- At specific timing points, the attack logic is executed:
  - Damage is applied
  - Knockback is applied
  - Special effects are triggered
- For multi-phase attacks (like spin_slam), different effects are applied at different timing points

### 6.4 Attack Completion

- When the attack timer reaches the attack duration, the attack is reset
- For healing, it transitions to upchuck attack instead of resetting
- The animation controller transitions back to idling or walking
- A cooldown is applied before the next attack can be selected

### 6.5 Stun Mechanics

- At 65% health, the stunned_standing state is triggered
- At 35% health, the stunned_sitting state is triggered
- These states cancel any ongoing attack
- The stun animation controller handles the transitions between stun phases
- After the stun completes, the entity returns to the idling state

### 6.6 Death Mechanics

- When health reaches 10 or below, `ptd_dbb:dead` is set to true
- The death animation plays
- Equipment is dropped
- The entity is removed

The attack cycle is a continuous loop of selection, execution, and completion, with interruptions for healing, stuns, and eventually death. The system ensures variety in attacks through history tracking and adapts to the player's distance from the boss.
