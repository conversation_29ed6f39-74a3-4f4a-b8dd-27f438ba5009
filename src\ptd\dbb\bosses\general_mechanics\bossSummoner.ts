
import { Dimension, Entity, EntityComponentTypes, ItemStack, system, Vector3 } from "@minecraft/server";
import { showcaseBossWithCinematicCamera } from "./cinematicCamera";

// Interface for a single item requirement
interface ItemRequirement {
  itemId: string;  // The item type ID to check for
  count?: number;  // Optional count of items required (default: 1)
}

// Interface for boss summoning requirements
interface BossSummoningRequirement {
  bossId: string;                  // The entity ID of the boss to summon
  baseItem: ItemRequirement;       // The base item that triggers the check
  adjacentItems: ItemRequirement[]; // The adjacent items required for summoning
  radius?: number;                 // Optional radius to check for adjacent items (default: 2)
}

// Map of boss summoning requirements
const BOSS_SUMMONING_REQUIREMENTS: BossSummoningRequirement[] = [
  // Piglin Champion
  {
    bossId: "ptd_dbb:piglin_champion",
    baseItem: { itemId: "minecraft:gold_block" },
    adjacentItems: [
      { itemId: "minecraft:gold_ingot" },
      { itemId: "minecraft:golden_axe" },
      { itemId: "minecraft:enchanted_golden_apple" },
      { itemId: "minecraft:honey_bottle" }
    ]
  },

  // Necromancer
  {
    bossId: "ptd_dbb:necromancer",
    baseItem: { itemId: "minecraft:soul_sand" },
    adjacentItems: [
      { itemId: "minecraft:bone" },
      { itemId: "minecraft:wither_rose" },
      { itemId: "minecraft:rotten_flesh" },
      { itemId: "minecraft:soul_lantern" }
    ]
  },

  // Grimhowl
  {
    bossId: "ptd_dbb:grimhowl",
    baseItem: { itemId: "minecraft:bone_block" },
    adjacentItems: [
      { itemId: "minecraft:bone" },
      { itemId: "minecraft:iron_sword" },
      { itemId: "minecraft:beef" },
      { itemId: "minecraft:ominous_bottle" }
    ]
  }
  // More bosses can be added here in the future
];

/**
 * Checks for boss summoning requirements in a dimension
 * @param dimension The dimension to check
 */
export async function checkBossSummoningRequirements(dimension: Dimension): Promise<void> {
  try {
    // Get all item entities in the dimension
    const itemEntities = dimension.getEntities({ type: "minecraft:item" });

    // Process each boss summoning requirement
    for (const requirement of BOSS_SUMMONING_REQUIREMENTS) {
      // Find base items
      const baseItems = findItemEntitiesOfType(itemEntities, requirement.baseItem.itemId);

      // Check each base item
      for (const baseItem of baseItems) {
        // Find adjacent items within the specified radius
        const radius = requirement.radius || 2;
        const adjacentItems = findAdjacentItemEntities(dimension, baseItem.location, radius);

        // Check if all required adjacent items are present
        const allItemsPresent = checkRequiredItems(adjacentItems, requirement.adjacentItems);

        if (allItemsPresent) {
          // Already define the base item's location to avoid errors in case
          // the base item gets removed while running the next functions
          const location = baseItem.location;

          // Remove all items involved in the summoning
          removeItems(baseItem, location, adjacentItems, requirement);

          // Play vanishing items particle 3 times
          for (let i = 0; i < 3; i++) {
            dimension.spawnParticle("minecraft:totem_particle", {x: location.x, y: location.y + 0.5, z: location.z});
          }
          
          // Play thunder sound effect
          dimension.playSound('ambient.weather.thunder', location);

          await system.waitTicks(40); // Wait for 2 seconds

          // Summon the boss
          summonBoss(dimension, location, requirement.bossId);

          await system.waitTicks(5); // Wait for 5 ticks

          // Add visual and sound effects
          addSummoningEffects(dimension, location);

        }
      }
    }
  } catch (error) {
    console.warn(`Error in boss summoning system: ${error}`);
  }
}

/**
 * Finds item entities of a specific type
 * @param itemEntities Array of item entities to check
 * @param itemId The item type ID to look for
 * @returns Array of matching item entities
 */
function findItemEntitiesOfType(itemEntities: Entity[], itemId: string): Entity[] {
  const matchingItems: Entity[] = [];

  for (const entity of itemEntities) {
    try {
      // Get the item component
      const itemComponent = entity.getComponent(EntityComponentTypes.Item);
      if (itemComponent && itemComponent.itemStack && itemComponent.itemStack.type.id === itemId) {
        matchingItems.push(entity);
      }
    } catch (error) {
      console.warn(`Error checking item entity: ${error}`);
    }
  }

  return matchingItems;
}

/**
 * Finds item entities within a radius of a location
 * @param dimension The dimension to check
 * @param location The center location
 * @param radius The radius to check
 * @returns Array of item entities within the radius
 */
function findAdjacentItemEntities(dimension: Dimension, location: Vector3, radius: number): Entity[] {
  return dimension.getEntities({
    location,
    maxDistance: radius,
    type: "minecraft:item"
  });
}

/**
 * Checks if all required items are present in the adjacent items
 * @param adjacentItems Array of adjacent item entities
 * @param requiredItems Array of required item types
 * @returns True if all required items are present, false otherwise
 */
function checkRequiredItems(adjacentItems: Entity[], requiredItems: ItemRequirement[]): boolean {
  // Create a map to track found items and their counts
  const foundItems = new Map<string, number>();

  // Count all adjacent items by type
  for (const entity of adjacentItems) {
    try {
      const itemComponent = entity.getComponent(EntityComponentTypes.Item);
      if (itemComponent && itemComponent.itemStack) {
        const itemId = itemComponent.itemStack.type.id;
        const count = foundItems.get(itemId) || 0;
        foundItems.set(itemId, count + itemComponent.itemStack.amount);
      }
    } catch (error) {
      console.warn(`Error checking adjacent item: ${error}`);
    }
  }

  // Check if all required items are present in sufficient quantities
  for (const requiredItem of requiredItems) {
    const requiredCount = requiredItem.count || 1;
    const foundCount = foundItems.get(requiredItem.itemId) || 0;

    if (foundCount < requiredCount) {
      return false;
    }
  }

  return true;
}

/**
 * Removes items involved in the boss summoning
 * @param baseItem The base item entity
 * @param adjacentItems Array of adjacent item entities
 * @param requirement The boss summoning requirement
 */
function removeItems(baseItem: Entity, location: Vector3, adjacentItems: Entity[], requirement: BossSummoningRequirement): void {
  // Remove the base item
  baseItem.remove();

  // Create a map to track which items need to be removed and how many
  const itemsToRemove = new Map<string, number>();
  for (const requiredItem of requirement.adjacentItems) {
    const itemId = requiredItem.itemId;
    const count = requiredItem.count || 1;
    itemsToRemove.set(itemId, count);
  }

  // Remove adjacent items as needed
  for (const entity of adjacentItems) {
    try {
      const itemComponent = entity.getComponent(EntityComponentTypes.Item);
      if (itemComponent && itemComponent.itemStack) {
        const itemId = itemComponent.itemStack.type.id;
        const remainingToRemove = itemsToRemove.get(itemId) || 0;

        if (remainingToRemove > 0) {
          // If we need to remove this item
          const itemAmount = itemComponent.itemStack.amount;

          if (itemAmount <= remainingToRemove) {
            // Remove the entire entity if we need all or more items than it has
            entity.remove();
            itemsToRemove.set(itemId, remainingToRemove - itemAmount);
          } else {
            // Otherwise, reduce the stack size by removing the entity and spawning a new one
            const newAmount = itemAmount - remainingToRemove;
            entity.dimension.spawnItem(new ItemStack(itemId, newAmount), location);
            entity.remove();
            itemsToRemove.set(itemId, 0);
          }
        }
      }
    } catch (error) {
      console.warn(`Error removing item: ${error}`);
    }
  }
}

/**
 * Summons a boss at the specified location and makes it face the nearest player
 * @param dimension The dimension to summon in
 * @param location The location to summon at
 * @param bossId The entity ID of the boss to summon
 */
function summonBoss(dimension: Dimension, location: Vector3, bossId: string): void {
  try {
    // Summon the boss slightly above the items to prevent collision
    const summonLocation: Vector3 = {
      x: location.x,
      y: location.y,
      z: location.z
    };

    // Find the nearest player to the summoning location using closest: 1
    const nearbyPlayers = dimension.getPlayers({
      location: summonLocation,
      maxDistance: 32, // Look for players within 32 blocks
      closest: 1 // Get only the closest player
    });

    // Spawn the boss entity
    const bossEntity = dimension.spawnEntity(bossId, summonLocation);


    if (bossEntity.typeId === "ptd_dbb:grimhowl") {
      showcaseBossWithCinematicCamera(
          bossEntity,
          {
          title: "Grimhowl",
          subtitle: "The Rage of the North",
          closeUpTimePercentage: 0.3,
          keyframeCount: 2,
          totalDuration: 7,
          semiCloseUpDistance: 9,
          closeUpDistance: 11,
          radius: 8
          }
      )
    }

    // If there are players nearby, make the boss face the nearest one
    if (nearbyPlayers.length > 0 && bossEntity) {
      // Get the nearest player (should be the only one in the array due to closest: 1)
      const nearestPlayer = nearbyPlayers[0];

      // Make sure we have a valid player
      if (nearestPlayer && nearestPlayer.location) {

        // Calculate the rotation to face the player
        //const dx = nearestPlayer.location.x - summonLocation.x;
        //const dz = nearestPlayer.location.z - summonLocation.z;

        // Calculate yaw (horizontal rotation) in degrees
        // Math.atan2 returns radians, so convert to degrees
        // Adjust for Minecraft's coordinate system
        //const yaw = (Math.atan2(dz, dx) * 180.0 / Math.PI) - 90;

        // Set the boss rotation
        // D.N Might be better this way
        bossEntity.teleport(bossEntity.location, {facingLocation:nearestPlayer.getHeadLocation()});
      }
    }
  } catch (error) {
    console.warn(`Error summoning boss: ${error}`);
  }
}

/**
 * Adds visual and sound effects for the boss summoning
 * @param dimension The dimension to add effects in
 * @param location The location to center effects at
 */
function addSummoningEffects(dimension: Dimension, location: Vector3): void {
  try {
    // Add particle effects
    dimension.spawnParticle("minecraft:large_explosion", location);

    // Add sound effects
    dimension.playSound("mob.wither.spawn", location);

  } catch (error) {
    console.warn(`Error adding summoning effects: ${error}`);
  }
}



