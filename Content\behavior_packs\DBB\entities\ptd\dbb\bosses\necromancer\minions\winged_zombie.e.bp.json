{"format_version": "1.21.80", "minecraft:entity": {"description": {"identifier": "ptd_dbb:winged_zombie", "is_spawnable": true, "is_summonable": true, "properties": {"ptd_dbb:spawning": {"type": "bool", "client_sync": true, "default": true}, "ptd_dbb:dead": {"type": "bool", "client_sync": true, "default": false}, "ptd_dbb:death_timer": {"type": "int", "client_sync": true, "default": 0, "range": [0, 100]}}}, "component_groups": {"ptd_dbb:spawning": {"minecraft:physics": {}, "minecraft:is_collidable": {}, "minecraft:timer": {"time": 1.7083, "looping": false, "time_down_event": {"event": "ptd_dbb:on_spawn", "target": "self"}}}, "ptd_dbb:default": {"minecraft:physics": {"has_collision": false}, "minecraft:attack": {"damage": 6}, "minecraft:behavior.delayed_attack": {"priority": 1, "attack_once": false, "track_target": true, "require_complete_path": false, "random_stop_interval": 0, "reach_multiplier": 1.8, "speed_multiplier": 1.18, "attack_duration": 1.5, "hit_delay_pct": 0.5, "cooldown_time": 0}, "minecraft:flying_speed": {"value": 0.2}, "minecraft:jump.static": {}, "minecraft:movement.hover": {}, "minecraft:navigation.hover": {"can_pass_doors": true, "can_break_doors": true, "can_jump": true, "can_path_over_water": true, "avoid_damage_blocks": true, "can_path_from_air": true, "avoid_water": false}, "minecraft:annotation.break_door": {"break_time": 12, "min_difficulty": "normal"}, "minecraft:behavior.break_door": {"priority": 4}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "must_see": false, "attack_interval": 1, "reselect_targets": true, "must_see_forget_duration": 0, "reevaluate_description": true, "entity_types": [{"priority": 0, "max_dist": 32, "filters": {"test": "is_family", "subject": "other", "value": "player"}}, {"priority": 1, "max_dist": 32, "filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "boss"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "necromancer"}, {"test": "bool_property", "domain": "ptd_dbb:spawning", "subject": "other", "operator": "==", "value": false}, {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "other", "operator": "==", "value": false}]}}, {"priority": 2, "max_dist": 32, "filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "minion"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "necromancer"}]}}]}, "minecraft:behavior.float": {"priority": 2}, "minecraft:behavior.random_look_around": {"priority": 3}, "minecraft:behavior.random_hover": {"priority": 2, "y_dist": 12}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 2}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "self", "value": true}}, "deals_damage": "no"}]}}, "ptd_dbb:dead": {"minecraft:physics": {}, "minecraft:is_collidable": {}, "minecraft:timer": {"time": 1.875, "looping": false, "time_down_event": {"event": "ptd_dbb:despawn", "target": "self"}}, "minecraft:movement": {"max": 0}, "minecraft:behavior.random_look_around": {"priority": 999999}, "minecraft:behavior.random_stroll": {"priority": 999999}, "minecraft:body_rotation_blocked": {}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 2}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "self", "value": true}}, "deals_damage": "no"}]}, "minecraft:navigation.hover": {"can_pass_doors": false, "can_break_doors": false, "can_jump": false, "can_path_over_water": false, "avoid_damage_blocks": false, "can_path_from_air": false, "avoid_water": true}}, "ptd_dbb:despawn": {"minecraft:instant_despawn": {}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ptd_dbb:spawning"]}}, "ptd_dbb:on_spawn": {"sequence": [{"remove": {"component_groups": ["ptd_dbb:spawning"]}}, {"set_property": {"ptd_dbb:spawning": false}}, {"add": {"component_groups": ["ptd_dbb:default"]}}]}, "ptd_dbb:dead": {"sequence": [{"set_property": {"ptd_dbb:dead": true}}, {"remove": {"component_groups": ["ptd_dbb:default"]}}, {"add": {"component_groups": ["ptd_dbb:dead", "ptd_dbb:default"]}}]}, "ptd_dbb:despawn": {"sequence": [{"add": {"component_groups": ["ptd_dbb:despawn"]}}]}}, "components": {"minecraft:collision_box": {"width": 0.7, "height": 1.9}, "minecraft:type_family": {"family": ["winged_zombie", "undead", "monster", "hostile", "minion", "necromancer"]}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 2}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:spawning", "subject": "self", "value": true}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "self", "value": true}}, "deals_damage": "no"}]}, "minecraft:health": {"value": 20, "max": 20}, "minecraft:follow_range": {"value": 128, "max": 128}, "minecraft:behavior.hurt_by_target": {"priority": 4, "entity_types": {"max_dist": 32, "must_see": false, "priority": 0, "must_see_forget_duration": 0, "filters": {"none_of": [{"test": "is_family", "subject": "other", "operator": "!=", "value": "necromancer"}]}}}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:is_stackable": {}, "minecraft:floats_in_liquid": {}, "minecraft:conditional_bandwidth_optimization": {}}}}