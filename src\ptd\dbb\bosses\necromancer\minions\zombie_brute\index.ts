import { Entity } from "@minecraft/server";
import { selectAttack, handleAttackLogic } from "./controller";

/**
 * Handles all mechanics for the Zombie Brute
 * This includes attack selection, attack execution, and cooldown management
 *
 * @param zombieBrute The zombie brute entity
 */
export function zombieBruteMechanics(zombieBrute: Entity): void {
  // Skip if entity is not valid
  try {
    if (!zombieBrute) return;

    // Skip if entity is spawning or dead
    const isSpawning = zombieBrute.getProperty("ptd_dbb:spawning") as boolean;
    const isDead = zombieBrute.getProperty("ptd_dbb:dead") as boolean;
    if (isSpawning || isDead) return;

    // Process attack cooldown
    const attackCooldown = zombieBrute.getProperty("ptd_dbb:attack_cooldown") as number;
    if (attackCooldown > 0) {
      zombieBrute.setProperty("ptd_dbb:attack_cooldown", attackCooldown - 1);
    }

    // Handle ongoing attacks
    const attack = zombieBrute.getProperty("ptd_dbb:attack") as string;
    if (attack !== "none") {
      handleAttackLogic(zombieBrute);
      return;
    }

    // Select and execute an attack if no attack is in progress
    selectAttack(zombieBrute);
  } catch (e) {
    return;
  }
}
