import { Entity } from "@minecraft/server";
import { getDistance } from "../../utilities/vector3";
import { stopVoidHydraSounds, VOID_HYDRA_ATTACK_SOUND_MAP } from "./soundManager";
import {
  getAvailableAttacks,
  updateAttackHistory,
  displayAttackHistory,
  SHORT_RANGE_ATTACKS,
  MEDIUM_RANGE_ATTACKS,
  LONG_RANGE_ATTACKS,
  UNREACHABLE_RANGE_ATTACKS
} from "./attackTracker";

/**
 * Attack ranges for different attack types
 */
export const ATTACK_RANGES = {
  close: { min: 0, max: 5 },
  medium: { min: 5, max: 10 },
  long: { min: 10, max: 16 },
  unreachable: { min: 16, max: 32 }
};

/**
 * Attack durations in ticks
 */
export const ATTACK_DURATIONS: Record<string, number> = {
  right_atomic_cross: 120,
  right_atomic: 100,
  right_vacuum: 150,
  right_summon: 180,
  mid_atomic: 100,
  mid_meteor: 160,
  mid_singularity: 200,
  left_atomic_cross: 120,
  left_atomic: 100,
  left_railgun: 140,
  left_missile: 170,
  left_shout: 130
};

/**
 * Attack timing points in ticks
 * When the damage and effects should be applied
 */
export const ATTACK_TIMINGS: Record<string, number> = {
  right_atomic_cross_damage: 60,
  right_atomic_damage: 50,
  right_vacuum_start: 30,
  right_vacuum_end: 120,
  right_summon_spawn: 90,
  mid_atomic_damage: 50,
  mid_meteor_spawn: 80,
  mid_singularity_start: 40,
  mid_singularity_end: 160,
  left_atomic_cross_damage: 60,
  left_atomic_damage: 50,
  left_railgun_fire: 70,
  left_missile_launch: 85,
  left_shout_effect: 65
};

/**
 * Sound effects for each attack type
 */
export const ATTACK_SOUND_MAP: Record<string, string> = VOID_HYDRA_ATTACK_SOUND_MAP;

/**
 * Selects an attack based on target distance
 * @param voidHydra The void hydra entity
 * @param target The target entity
 */
export function selectAttack(voidHydra: Entity, target: Entity): void {
  // Check if the attack cooldown is 0
  const attackCooldown = voidHydra.getProperty("ptd_dbb:attack_cooldown") as number;
  if (attackCooldown > 0) {
    return; // Don't select an attack if the cooldown is not 0
  }

  const distance = getDistance(voidHydra.location, target.location);
  let selectedAttack = null;

  // Short range (0-5 blocks): select from available attacks based on usage history
  if (distance >= ATTACK_RANGES.close.min && distance <= ATTACK_RANGES.close.max) {
    // Get available attacks based on usage history
    const availableAttacks = getAvailableAttacks(voidHydra, "short", SHORT_RANGE_ATTACKS);
    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;
      selectedAttack = attack;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopVoidHydraSounds(voidHydra, target, attackSound);

      // Trigger the attack event
      voidHydra.triggerEvent(`ptd_dbb:${attack}_attack`);

      // Set the attack property
      voidHydra.setProperty("ptd_dbb:attack", attack);
      voidHydra.setProperty("ptd_dbb:attack_timer", 0);

      // Update attack history
      updateAttackHistory(voidHydra, "short", attack);
      // Display attack history on the actionbar
      displayAttackHistory(voidHydra, "short");
    }
  }
  // Medium range (5-10 blocks): select from available attacks based on usage history
  else if (distance > ATTACK_RANGES.medium.min && distance <= ATTACK_RANGES.medium.max) {
    // Get available attacks based on usage history
    const availableAttacks = getAvailableAttacks(voidHydra, "medium", MEDIUM_RANGE_ATTACKS);
    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;
      selectedAttack = attack;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopVoidHydraSounds(voidHydra, target, attackSound);

      // Trigger the attack event
      voidHydra.triggerEvent(`ptd_dbb:${attack}_attack`);

      // Set the attack property
      voidHydra.setProperty("ptd_dbb:attack", attack);
      voidHydra.setProperty("ptd_dbb:attack_timer", 0);

      // Update attack history
      updateAttackHistory(voidHydra, "medium", attack);
      // Display attack history on the actionbar
      displayAttackHistory(voidHydra, "medium");
    }
  }
  // Long range (10-16 blocks): select from available attacks based on usage history
  else if (distance > ATTACK_RANGES.long.min && distance <= ATTACK_RANGES.long.max) {
    // Get available attacks based on usage history
    const availableAttacks = getAvailableAttacks(voidHydra, "long", LONG_RANGE_ATTACKS);
    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;
      selectedAttack = attack;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopVoidHydraSounds(voidHydra, target, attackSound);

      // Trigger the attack event
      voidHydra.triggerEvent(`ptd_dbb:${attack}_attack`);

      // Set the attack property
      voidHydra.setProperty("ptd_dbb:attack", attack);
      voidHydra.setProperty("ptd_dbb:attack_timer", 0);

      // Update attack history
      updateAttackHistory(voidHydra, "long", attack);
      // Display attack history on the actionbar
      displayAttackHistory(voidHydra, "long");
    }
  }
  // Unreachable range (16-32 blocks): select from available attacks based on usage history
  else if (distance > ATTACK_RANGES.unreachable.min && distance <= ATTACK_RANGES.unreachable.max) {
    // Get available attacks based on usage history
    const availableAttacks = getAvailableAttacks(voidHydra, "unreachable", UNREACHABLE_RANGE_ATTACKS);
    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;
      selectedAttack = attack;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopVoidHydraSounds(voidHydra, target, attackSound);

      // Trigger the attack event
      voidHydra.triggerEvent(`ptd_dbb:${attack}_attack`);

      // Set the attack property
      voidHydra.setProperty("ptd_dbb:attack", attack);
      voidHydra.setProperty("ptd_dbb:attack_timer", 0);

      // Update attack history
      updateAttackHistory(voidHydra, "unreachable", attack);
      // Display attack history on the actionbar
      displayAttackHistory(voidHydra, "unreachable");
    }
  }

  // Apply slowness effect if an attack was selected
  if (selectedAttack && selectedAttack in ATTACK_DURATIONS) {
    const attackDuration = ATTACK_DURATIONS[selectedAttack];
    if (attackDuration) {
      // Apply slowness with amplifier 250 for the duration of the attack
      // This effectively prevents movement during the attack
      voidHydra.addEffect("minecraft:slowness", attackDuration, { amplifier: 250, showParticles: false });
    }
  }
}

/**
 * Handles attack logic based on attack type and timer
 * @param voidHydra The necromancer entity
 * @param attack The current attack type
 * @param attackTimer The current attack timer
 */
export function handleAttackLogic(voidHydra: Entity, attack: string, attackTimer: number): void {
  // Reset attack when animation is complete
  const duration = ATTACK_DURATIONS[attack];
  if (duration !== undefined && attackTimer >= duration) {
    // Stop all sounds when resetting attack
    stopVoidHydraSounds(voidHydra);

    // Reset attack and set cooldown
    voidHydra.triggerEvent("ptd_dbb:reset_attack");

    // Set cooldown (40 ticks)
    voidHydra.setProperty("ptd_dbb:attack_cooldown", 40);
  }
}
