import { Entity } from "@minecraft/server";
import { piglinChampionMechanics } from "./piglin_champion/index";
import { necromancerMechanics } from "./necromancer/index";
import { voidHydraMechanics } from "./void_hydra/index";

export const bossIds = new Set<string>([
  "ptd_dbb:piglin_champion",
  "ptd_dbb:necromancer",
  "ptd_dbb:wardzilla",
  "ptd_dbb:void_hydra",
  "ptd_dbb:grimhowl"
]);

export function handleBossMechanics(entity: Entity, bossTypeid: string) {
  // Handle piglin champion mechanics
  if (bossTypeid === "ptd_dbb:piglin_champion") {
    piglinChampionMechanics(entity);
  }

  // Handle necromancer mechanics
  if (bossTypeid === "ptd_dbb:necromancer") {
    necromancerMechanics(entity);
  }

  // Handle void hydra mechanics
  if (bossTypeid === "ptd_dbb:void_hydra") {
    voidHydraMechanics(entity);
  }
}
