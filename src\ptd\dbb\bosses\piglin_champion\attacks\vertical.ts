import { <PERSON><PERSON><PERSON>, <PERSON>tity, <PERSON><PERSON>ty<PERSON><PERSON>ge<PERSON><PERSON><PERSON>, GameMode, Player, Vector3, system } from "@minecraft/server";
import { getDistance, getDirection } from "../../../utilities/vector3";
import { NON_SOLID_BLOCKS } from "../../../utilities/constants/nonSolidBlocks";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";

/**
 * Checks if a block at the given location is an air block or a non-solid block
 * If it's a non-solid block, breaks it to simulate physics with particles
 * @param dimension The dimension to check in
 * @param location The location to check
 * @returns True if the block is an air block or a non-solid block, false otherwise
 */
function isAirOrNonSolid(dimension: Dimension, location: Vector3): boolean {
  const block = dimension.getBlock(location);
  if (!block) return true; // Treat as air if block is null
  if (block.isAir) return true;

  // If it's a non-solid block, break it to simulate physics
  if (NON_SOLID_BLOCKS.has(block.type.id)) {
    try {
      // Break the block using the setblock command with destroy flag for particles
      const x = Math.floor(location.x);
      const y = Math.floor(location.y);
      const z = Math.floor(location.z);
      dimension.runCommand(`setblock ${x} ${y} ${z} air [] destroy`);
    } catch (error) {
      console.warn(`Error breaking non-solid block with command: ${error}`);

      // Fallback to using setType if the command fails
      try {
        block.setType("minecraft:air");
      } catch (fallbackError) {
        console.warn(`Fallback error breaking non-solid block: ${fallbackError}`);
      }
    }
    return true;
  }

  return false;
}

/**
 * Finds a position just above the ground (solid block) at the given x,z coordinates
 * @param dimension The dimension to check in
 * @param location The base location to check
 * @param maxSearchDistance Maximum vertical distance to search
 * @returns A location 0.001 blocks above a solid block, or undefined if none found
 */
function findGroundPosition(
  dimension: Dimension,
  location: Vector3,
  maxSearchDistance: number = 20
): Vector3 | undefined {
  // Start from a higher position to ensure we find the ground in most cases
  const startY = location.y + 10;

  // Search downward for a solid block
  for (let y = 0; y <= maxSearchDistance * 2; y++) {
    const checkPos: Vector3 = {
      x: location.x,
      y: startY - y,
      z: location.z
    };

    // Get the block at this position
    const block = dimension.getBlock(checkPos);

    // If we found a non-air block
    if (block && !block.isAir) {
      // Check if it's a non-solid block (grass, flower, etc.)
      if (NON_SOLID_BLOCKS.has(block.type.id)) {
        // Instead of returning, continue searching downward
        continue;
      }

      // If it's a solid block, check if the block above is air or non-solid
      const blockAbovePos: Vector3 = {
        x: location.x,
        y: startY - y + 1,
        z: location.z
      };

      // Only return this position if the block above is air or a non-solid block
      if (isAirOrNonSolid(dimension, blockAbovePos)) {
        return {
          x: location.x,
          y: startY - y + 1.001, // Position slightly above the solid block
          z: location.z
        };
      }

      // If the block above isn't air or non-solid, continue searching downward
    }
  }

  // If we couldn't find a suitable ground position, return the original location
  return location;
}

/**
 * Executes the vertical attack (ground slam) for the Piglin Champion
 * Applies damage and upward knockback to nearby entities based on the direction to the target
 * Also spawns a wave of rocks that deal additional damage and knockback
 *
 * @param piglinChampion The piglin champion entity
 * @param target The target entity
 */
export function executeVerticalAttack(piglinChampion: Entity, target: Entity): void {
  const damageRadius = 5;
  // Use direct damage value instead of percentage for the axe part
  const damage = PIGLIN_CHAMPION_ATTACK_DAMAGES.vertical.axe.damage;

  // Calculate direction from piglin to target using the utility function
  const direction = getDirection(piglinChampion.location, target.location);
  const dirX = direction.x;
  const dirZ = direction.z;

  // Calculate position 4 blocks in front of the piglin in the direction of the target
  const originPos: Vector3 = {
    x: piglinChampion.location.x + dirX * 4,
    y: piglinChampion.location.y,
    z: piglinChampion.location.z + dirZ * 4
  };

  // Apply damage to nearby entities
  piglinChampion.dimension
    .getEntities({
      location: originPos,
      maxDistance: damageRadius,
      excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
      excludeFamilies: ["piglin_champion", "piglin", "rock"]
    })
    .forEach((entity) => {
      // Apply damage
      entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });

      // Create 2D points (same y-coordinate) to calculate horizontal distance
      const point1: Vector3 = { x: entity.location.x, y: 0, z: entity.location.z };
      const point2: Vector3 = { x: originPos.x, y: 0, z: originPos.z };
      const distance = getDistance(point1, point2);

      if (distance > 0) {
        // Calculate direction from entity to target for knockback using the utility function
        const knockbackDir = getDirection(entity.location, target.location);

        // Use the calculated direction or fall back to the original direction if zero
        const nx = knockbackDir.x || dirX;
        const nz = knockbackDir.z || dirZ;

        // Vertical attack parameters - higher vertical component for the shockwave effect
        const horizontalStrength = 2.0;
        const verticalStrength = 1.5; // Increased to fling players 10 blocks into the air

        try {
          // Try to apply knockback first
          if (entity instanceof Player) {
            const gameMode = entity.getGameMode();
            if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
              entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
            }
          } else {
            entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
          }
        } catch (e) {
          // Fallback to applyImpulse if applyKnockback fails
          const impulse: Vector3 = {
            x: nx * horizontalStrength,
            y: verticalStrength,
            z: nz * horizontalStrength
          };

          entity.applyImpulse(impulse);
        }
      }
    });

  // Number of rocks to spawn in the wave
  const rockCount = 5;

  // Play a sound effect for the rock wave
  piglinChampion.dimension.playSound("random.explode", originPos);

  // Spawn rocks sequentially in a straight line
  spawnRocksSequentially(piglinChampion, originPos, { x: dirX, z: dirZ }, rockCount, target);
}

/**
 * Spawns rocks sequentially in a straight line with a delay between each spawn
 * @param piglinChampion The piglin champion entity
 * @param originPos The origin position for the attack
 * @param direction The direction vector (x and z components)
 * @param rockCount The number of rocks to spawn
 * @param target The target entity
 */
async function spawnRocksSequentially(
  piglinChampion: Entity,
  originPos: Vector3,
  direction: { x: number; z: number },
  rockCount: number,
  target: Entity
): Promise<void> {
  // Base distance from origin (2 blocks)
  const baseDistance = 2;
  // Distance between each rock (2 blocks)
  const distanceBetweenRocks = 2;

  // Spawn rocks one by one with a delay
  for (let i = 0; i < rockCount; i++) {
    // Calculate the position for this rock (straight line)
    const distance = baseDistance + i * distanceBetweenRocks;
    const rockPos: Vector3 = {
      x: originPos.x + direction.x * distance,
      y: originPos.y,
      z: originPos.z + direction.z * distance
    };

    // Find a position just above the ground for spawning the rock
    const spawnPos = findGroundPosition(piglinChampion.dimension, rockPos);

    // Only spawn the rock if we found a valid position
    if (spawnPos) {
      // Spawn rock entity
      const rock = piglinChampion.dimension.spawnEntity("ptd_dbb:rock", spawnPos);

      if (rock) {
        // Play spawn sound
        piglinChampion.dimension.playSound("item.trident.throw", spawnPos);

        const rockRadius = 4; // Radius around the rock to check for entities
        piglinChampion.dimension
          .getEntities({
            location: rock.location,
            maxDistance: rockRadius,
            excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
            excludeFamilies: ["piglin_champion", "piglin", "rock"]
          })
          .forEach((entity) => {
            // Use direct damage value for the rocks part
            const rockDamage = PIGLIN_CHAMPION_ATTACK_DAMAGES.vertical.rocks.damage;
            entity.applyDamage(rockDamage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });

            // Apply the same upward knockback as the initial attack
            const horizontalStrength = 1.7;
            const verticalStrength = 1.2;

            // Calculate direction from entity to target for knockback using the utility function
            const knockbackDir = getDirection(entity.location, target.location);

            // Use the calculated direction or fall back to the original direction if zero
            const knockbackDirX = knockbackDir.x || direction.x;
            const knockbackDirZ = knockbackDir.z || direction.z;

            try {
              // Try to apply knockback first
              if (entity instanceof Player) {
                const gameMode = entity.getGameMode();
                if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                  entity.applyKnockback(knockbackDirX, knockbackDirZ, horizontalStrength, verticalStrength);
                }
              } else {
                entity.applyKnockback(knockbackDirX, knockbackDirZ, horizontalStrength, verticalStrength);
              }
            } catch (e) {
              // Fallback to applyImpulse if applyKnockback fails
              const impulse: Vector3 = {
                x: knockbackDirX * horizontalStrength,
                y: verticalStrength,
                z: knockbackDirZ * horizontalStrength
              };

              entity.applyImpulse(impulse);
            }
          });
      }
    }

    // Wait 1 tick before spawning the next rock
    await system.waitTicks(1);
  }
}
