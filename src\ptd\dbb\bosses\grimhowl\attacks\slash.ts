/**
 * Grimhowl boss slash attack implementation.
 * This file contains the logic for executing the slash attack,
 * which propels the boss forward and damages entities in its path.
 */
import { system, Entity, Vector3, EntityDamageCause } from "@minecraft/server";
import { filterValidTargets } from "../constants_utils";
import { lightDamage, heavyDamage } from "../index";

/**
 * Executes the slash attack for the Grimhowl boss
 * The attack propels the boss forward toward a target and damages
 * entities in its path, with different behavior based on boss state
 *
 * @param sourceEntity - The Grimhowl boss entity
 */
export function doGrimhowlSlash(sourceEntity: Entity): void {
    if (!sourceEntity) return;

    const isEnraged: boolean = sourceEntity.getProperty('ptd_dbb:enraged') as boolean;
    const defaultFacingLocation: Vector3 = sourceEntity.getViewDirection();

    sourceEntity.triggerEvent(isEnraged ? 'ptd_dbb:grimhowl_slash_enraged' : 'ptd_dbb:grimhowl_slash');

    const nearbyTargets: Entity[] = sourceEntity.dimension.getEntities({
        location: sourceEntity.location,
        excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
        maxDistance: 16
    }).filter(filterValidTargets(sourceEntity));

    if (nearbyTargets.length === 0) {
        sourceEntity.triggerEvent('ptd_dbb:attack_done');
        return;
    }

    sourceEntity.teleport(sourceEntity.location, { facingLocation: nearbyTargets[0] ? nearbyTargets[0].location : defaultFacingLocation, keepVelocity: true });
    sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);

    system.runTimeout(() => {
        if (!sourceEntity) return;

        const viewDirection: Vector3 = sourceEntity.getViewDirection();
        const impulse: Vector3 = {
            x: viewDirection.x * (sourceEntity.getProperty('ptd_dbb:sword_mode') ? 2.5 : 4),
            y: 0.1,
            z: viewDirection.z * (sourceEntity.getProperty('ptd_dbb:sword_mode') ? 2.5 : 4)
        };
        sourceEntity.applyImpulse(impulse);

        const collideTargets: Entity[] = sourceEntity.dimension.getEntities({
            location: sourceEntity.location,
            excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
            maxDistance: 4.4
        }).filter(filterValidTargets(sourceEntity));

        collideTargets.forEach((entity: Entity) => {
            entity.applyDamage(lightDamage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
        });
    }, isEnraged ? 5 : 10);

    system.runTimeout(() => {
        if (!sourceEntity) return;

        if (sourceEntity.getProperty('ptd_dbb:sword_mode')) {
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.sword_swipe @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
        } else {
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
            sourceEntity.runCommand(`playsound mob.ravager.bite @a ~ ~ ~ 10 ${2 + (Math.random() * 0.3)}`);
        }
        const viewDirection: Vector3 = sourceEntity.getViewDirection();
        const forwardLocation: Vector3 = {
            x: sourceEntity.location.x + viewDirection.x * 5,
            y: sourceEntity.location.y,
            z: sourceEntity.location.z + viewDirection.z * 5
        };

        const collideTargets: Entity[] = sourceEntity.dimension.getEntities({
            location: sourceEntity.location,
            excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
            maxDistance: 4.4
        }).filter(filterValidTargets(sourceEntity));

        const frontTargets: Entity[] = sourceEntity.dimension.getEntities({
            location: forwardLocation,
            excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
            maxDistance: (sourceEntity.getProperty('ptd_dbb:sword_mode') ? 5 : 2.5)
        }).filter(filterValidTargets(sourceEntity));

        collideTargets.forEach((entity: Entity) => {
            entity.applyDamage(lightDamage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
        });

        frontTargets.forEach((entity: Entity) => {
            entity.applyDamage(heavyDamage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
        });
    }, isEnraged ? 10 : 20);
}
