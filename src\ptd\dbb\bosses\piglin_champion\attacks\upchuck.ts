import { Entity, Vector3, system } from "@minecraft/server";

/**
 * Executes the upchuck attack for the Piglin Champion
 * Applies poison effect to nearby entities
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeUpchuckAttack(piglinChampion: Entity): void {
    // Apply poison to nearby entities
    const poisonRadius = 6;
    const poisonDuration = 60; // 3 seconds at 20 ticks per second
    const poisonAmplifier = 1; // Poison II

    // Get direction vector directly from the piglin's view direction
    const viewDirection = piglinChampion.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;

    // Calculate position 2 blocks in front of the piglin as the origin for the attack
    const originPos: Vector3 = {
        x: piglinChampion.location.x + (dirX * 2),
        y: piglinChampion.location.y,
        z: piglinChampion.location.z + (dirZ * 2)
    };

    // Spawn poison cloud particles at the origin
    piglinChampion.dimension.spawnParticle("minecraft:mob_portal", originPos);

    // Play a sound effect for the poison attack
    piglinChampion.dimension.playSound("mob.slime.attack", originPos);

    // Start continuous poison application
    startContinuousPoisonEffect(piglinChampion, originPos, poisonRadius, poisonDuration, poisonAmplifier);
}
/**
 * Continuously applies poison effect to entities within a radius
 * Starts at tick 55 and continues until the animation ends (170 ticks)
 *
 * @param piglinChampion The piglin champion entity
 * @param originPos The origin position for the poison effect
 * @param radius The radius of the poison effect
 * @param duration The duration of each poison effect application
 * @param amplifier The amplifier of the poison effect
 */
async function startContinuousPoisonEffect(
    piglinChampion: Entity,
    originPos: Vector3,
    radius: number,
    duration: number,
    amplifier: number
): Promise<void> {
    // Get the current attack timer
    let attackTimer = piglinChampion.getProperty("ptd_dbb:attack_timer") as number;

    // Wait until tick 55 before starting the continuous poison effect
    while (attackTimer < 55) {
        await system.waitTicks(1);
        try {
            // Check if the entity is still valid and the attack is still "upchuck"
            const attack = piglinChampion.getProperty("ptd_dbb:attack") as string;
            if (attack !== "upchuck") return;

            attackTimer = piglinChampion.getProperty("ptd_dbb:attack_timer") as number;
        } catch (e) {
            // Entity might have been removed
            return;
        }
    }

    // Apply poison effect continuously until the animation ends (170 ticks)
    const intervalId = system.runInterval(() => {
        try {
            // Check if the entity is still valid and the attack is still "upchuck"
            const attack = piglinChampion.getProperty("ptd_dbb:attack") as string;
            const currentTimer = piglinChampion.getProperty("ptd_dbb:attack_timer") as number;

            if (attack !== "upchuck" || currentTimer >= 170) {
                system.clearRun(intervalId);
                return;
            }

            // Apply poison to entities within the radius
            piglinChampion.dimension.getEntities({
                location: originPos,
                maxDistance: radius,
                excludeFamilies: ["piglin_champion", "piglin", "rock", "inanimate"]
            }).forEach(entity => {
                // Apply poison only to entities that are not XP orbs or items
                if (entity.typeId !== "minecraft:xp_orb" && entity.typeId !== "minecraft:item") {
                    // Apply poison effect
                    entity.addEffect("minecraft:fatal_poison", duration, { amplifier: amplifier, showParticles: true });
                }
            });

            // Spawn poison cloud particles at the origin
            piglinChampion.dimension.spawnParticle("minecraft:mob_portal", originPos);
        } catch (e) {
            // Entity might have been removed
            system.clearRun(intervalId);
        }
    }, 1); // Apply poison effect every tick
}