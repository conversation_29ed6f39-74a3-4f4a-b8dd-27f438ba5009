{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb_piglin_champion_minion.attack": {"initial_state": "default", "states": {"default": {"transitions": [{"hand_attack": "q.has_target && !query.is_item_equipped && variable.attack_time >= 0.0 && !query.is_admiring"}, {"melee_attack": "q.has_target && query.is_item_equipped && variable.attack_time >= 0.0 && !query.is_admiring"}]}, "melee_attack": {"animations": ["melee_attack"], "transitions": [{"default": "!query.is_item_equipped || !variable.has_target || variable.attack_time < 0.0 ||  query.is_admiring"}]}, "hand_attack": {"animations": ["hand_attack"], "transitions": [{"default": "query.is_item_equipped || !variable.has_target || variable.attack_time < 0.0 ||  query.is_admiring"}]}}}}}